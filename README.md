# AI Website Redesigner Chrome Extension

A powerful Chrome extension that uses Google's Gemini AI to redesign any website with modern, beautiful styling based on your custom prompts.

## Features

- 🎨 **AI-Powered Redesign**: Uses Google Gemini API to intelligently redesign websites
- 🎯 **Custom Prompts**: Describe exactly how you want the website to look
- 📱 **Responsive Design**: Automatically makes websites mobile-friendly
- 🔄 **Easy Revert**: One-click revert to original design
- 💾 **Content Preservation**: Keeps all original content and functionality intact
- ⚡ **Real-time Preview**: See changes applied instantly
- 🔒 **Secure**: API key stored locally, no data sent to third parties

## Installation

1. **Get a Gemini API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the key for use in the extension

2. **Install the Extension**:
   - Download or clone this repository
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" in the top right corner
   - Click "Load unpacked" and select the extension directory
   - The extension icon should appear in your toolbar

3. **Setup**:
   - Click the extension icon
   - Enter your Gemini API key in the configuration section
   - Click "Save" to store your API key

## Usage

1. **Navigate to any website** you want to redesign
2. **Click the extension icon** to open the popup
3. **Enter your redesign prompt** in the text area, for example:
   - "Make it more modern and minimalist"
   - "Improve the color scheme and typography"
   - "Add better spacing and visual hierarchy"
   - "Make it look like a premium SaaS product"
4. **Configure options**:
   - ✅ Preserve original content (recommended)
   - ✅ Make responsive (recommended)
5. **Click "Redesign Website"** and wait for the AI to work its magic
6. **Review the results** and use "Revert Changes" if needed

## Example Prompts

Here are some effective prompts to try:

### Design Styles
- "Make it look like a modern SaaS landing page"
- "Apply a dark theme with neon accents"
- "Use a minimalist design with lots of white space"
- "Make it look like a premium e-commerce site"

### Specific Improvements
- "Improve the typography and make text more readable"
- "Add better visual hierarchy with proper spacing"
- "Make the color scheme more professional"
- "Improve the navigation and make it more intuitive"

### Layout Changes
- "Convert to a single-page layout"
- "Make it mobile-first responsive"
- "Add a modern hero section"
- "Improve the footer design"

## Technical Details

### Architecture
- **Manifest V3**: Uses the latest Chrome extension architecture
- **Content Scripts**: Safely manipulate webpage DOM
- **Background Service Worker**: Handles API communication
- **Popup Interface**: Modern, responsive UI for user interaction

### API Integration
- Uses Google Gemini Pro model for intelligent redesign
- Implements proper error handling and rate limiting
- Secure API key storage using Chrome's sync storage

### Safety Features
- Automatic backup of original HTML
- Content validation before applying changes
- Graceful error handling and user feedback
- No external data transmission except to Gemini API

## File Structure

```
ai-website-redesigner/
├── manifest.json              # Extension configuration
├── background.js              # Service worker for API calls
├── content.js                 # DOM manipulation script
├── popup/
│   ├── popup.html            # Extension popup interface
│   ├── popup.css             # Modern styling
│   └── popup.js              # Popup logic and communication
├── icons/
│   ├── icon16.png            # Extension icons (multiple sizes)
│   ├── icon32.png
│   ├── icon48.png
│   ├── icon128.png
│   └── create-icons.html     # Icon generator utility
└── README.md                 # This file
```

## Development

### Prerequisites
- Chrome browser
- Gemini API key
- Basic knowledge of HTML/CSS/JavaScript

### Local Development
1. Clone the repository
2. Make your changes
3. Load the extension in Chrome developer mode
4. Test on various websites
5. Check the console for any errors

### Building Icons
Open `icons/create-icons.html` in your browser to generate the required icon files.

## Troubleshooting

### Common Issues

**Extension not loading:**
- Ensure all files are present
- Check Chrome developer console for errors
- Verify manifest.json syntax

**API errors:**
- Verify your Gemini API key is correct
- Check your internet connection
- Ensure you have API quota remaining

**Redesign not applying:**
- Check if the website blocks content modification
- Try refreshing the page and trying again
- Some dynamic websites may not work perfectly

**Revert not working:**
- Refresh the page to restore original state
- The extension stores backup automatically

## Privacy & Security

- Your API key is stored locally in Chrome's secure storage
- No website data is sent anywhere except to Google's Gemini API
- The extension only modifies the visual presentation, not functionality
- All changes are temporary and can be reverted instantly

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## License

This project is open source and available under the MIT License.

## Disclaimer

This extension modifies website appearance temporarily. Always respect website terms of service and use responsibly. The extension is for personal use and educational purposes.
