# AI Website Redesigner Chrome Extension

A powerful Chrome extension that uses Google's Gemini AI to redesign any website with modern, beautiful styling based on your custom prompts.

## Features

- 🎨 **AI-Powered Redesign**: Uses Google Gemini 2.5 Pro with advanced thinking capabilities
- 🔍 **Smart Design Research**: Leverages Google Search integration for current design trends
- 🎯 **Custom Prompts**: Describe exactly how you want the website to look
- 📱 **Responsive Design**: Automatically makes websites mobile-friendly
- 🔄 **Easy Revert**: One-click revert to original design
- 💾 **Content Preservation**: Keeps all original content and functionality intact
- ⚡ **Real-time Preview**: See changes applied instantly with streaming responses
- 🎭 **Modern Design Trends**: AI researches and applies current design best practices
- 🔒 **Secure**: API key stored locally, no data sent to third parties

## Installation

1. **Get a Gemini API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the key for use in the extension

2. **Install the Extension**:
   - Download or clone this repository
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" in the top right corner
   - Click "Load unpacked" and select the extension directory
   - The extension icon should appear in your toolbar

3. **Setup**:
   - Click the extension icon
   - Enter your Gemini API key in the configuration section
   - Click "Save" to store your API key

## Usage

1. **Navigate to any website** you want to redesign
2. **Click the extension icon** to open the popup
3. **Enter your redesign prompt** in the text area, for example:
   - "Make it more modern and minimalist"
   - "Improve the color scheme and typography"
   - "Add better spacing and visual hierarchy"
   - "Make it look like a premium SaaS product"
4. **Configure options**:
   - ✅ Preserve original content (recommended)
   - ✅ Make responsive (recommended)
5. **Click "Redesign Website"** and wait for the AI to work its magic
6. **Review the results** and use "Revert Changes" if needed

## Example Prompts

Here are some effective prompts to try with the enhanced Gemini 2.5 Pro:

### Design Styles (AI will research current trends)
- "Make it look like a modern SaaS landing page with current 2024 design trends"
- "Apply a dark theme with neon accents inspired by popular tech companies"
- "Use a minimalist design following current Scandinavian design principles"
- "Make it look like a premium e-commerce site with modern luxury aesthetics"
- "Apply the latest fintech app design patterns and color schemes"

### Specific Improvements (AI-enhanced)
- "Research and apply the best typography trends for 2024, improve readability"
- "Add better visual hierarchy using current design system principles"
- "Update the color scheme with modern, accessible color palettes"
- "Improve the navigation following current UX best practices"
- "Apply modern micro-interactions and subtle animations"

### Layout Changes (Trend-aware)
- "Convert to a single-page layout using current hero section trends"
- "Make it mobile-first responsive with modern grid systems"
- "Add a contemporary hero section with current visual trends"
- "Redesign the footer with modern social proof and trust signals"
- "Apply current card-based design patterns throughout"

## Technical Details

### Architecture
- **Manifest V3**: Uses the latest Chrome extension architecture
- **Content Scripts**: Safely manipulate webpage DOM
- **Background Service Worker**: Handles API communication
- **Popup Interface**: Modern, responsive UI for user interaction

### API Integration
- Uses Google Gemini 2.5 Pro model with advanced reasoning capabilities
- Streaming API integration for real-time response processing
- Google Search tool integration for researching current design trends
- Advanced thinking budget configuration for better design decisions
- Implements proper error handling and rate limiting
- Secure API key storage using Chrome's sync storage

### Safety Features
- Automatic backup of original HTML
- Content validation before applying changes
- Graceful error handling and user feedback
- No external data transmission except to Gemini API

## File Structure

```
ai-website-redesigner/
├── manifest.json              # Extension configuration
├── background.js              # Service worker for API calls
├── content.js                 # DOM manipulation script
├── popup/
│   ├── popup.html            # Extension popup interface
│   ├── popup.css             # Modern styling
│   └── popup.js              # Popup logic and communication
├── icons/
│   ├── icon16.png            # Extension icons (multiple sizes)
│   ├── icon32.png
│   ├── icon48.png
│   ├── icon128.png
│   └── create-icons.html     # Icon generator utility
└── README.md                 # This file
```

## Development

### Prerequisites
- Chrome browser
- Gemini API key
- Basic knowledge of HTML/CSS/JavaScript

### Local Development
1. Clone the repository
2. Make your changes
3. Load the extension in Chrome developer mode
4. Test on various websites
5. Check the console for any errors

### Building Icons
Open `icons/create-icons.html` in your browser to generate the required icon files.

## Troubleshooting

### Common Issues

**Extension not loading:**
- Ensure all files are present
- Check Chrome developer console for errors
- Verify manifest.json syntax

**API errors:**
- Verify your Gemini API key is correct
- Check your internet connection
- Ensure you have API quota remaining

**Redesign not applying:**
- Check if the website blocks content modification
- Try refreshing the page and trying again
- Some dynamic websites may not work perfectly

**Revert not working:**
- Refresh the page to restore original state
- The extension stores backup automatically

## Privacy & Security

- Your API key is stored locally in Chrome's secure storage
- No website data is sent anywhere except to Google's Gemini API
- The extension only modifies the visual presentation, not functionality
- All changes are temporary and can be reverted instantly

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## License

This project is open source and available under the MIT License.

## Disclaimer

This extension modifies website appearance temporarily. Always respect website terms of service and use responsibly. The extension is for personal use and educational purposes.
