# Testing Guide for AI Website Redesigner Chrome Extension

This guide will help you test the Chrome extension functionality step by step.

## Prerequisites

1. **Chrome Browser** (latest version recommended)
2. **Gemini API Key** from [Google AI Studio](https://makersuite.google.com/app/apikey)
3. **Extension Files** (all files in this directory)

## Step 1: Generate Extension Icons

Before loading the extension, you need to create the required icon files:

1. Open `icons/create-icons.html` in your web browser
2. Click "Download All Icons" or download each size individually
3. Save all downloaded PNG files in the `icons/` directory:
   - `icon16.png`
   - `icon32.png` 
   - `icon48.png`
   - `icon128.png`

## Step 2: Load Extension in Chrome

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable **"Developer mode"** toggle in the top right corner
3. Click **"Load unpacked"** button
4. Select the root directory of this extension (containing `manifest.json`)
5. The extension should appear in your extensions list
6. Pin the extension to your toolbar for easy access

## Step 3: Configure API Key

1. Click the extension icon in your Chrome toolbar
2. In the popup, enter your Gemini API key in the "API Configuration" section
3. Click **"Save"** button
4. Wait for the green success message confirming the API key is valid

## Step 4: Test Basic Functionality

### Test 1: Simple Website Redesign

1. Navigate to a simple website (e.g., `https://example.com`)
2. Click the extension icon
3. Enter a simple prompt: "Make it more modern and colorful"
4. Ensure both checkboxes are checked:
   - ✅ Preserve original content
   - ✅ Make responsive
5. Click **"Redesign Website"**
6. Wait for the process to complete (should take 10-30 seconds)
7. Verify the website appearance has changed
8. Click **"Revert Changes"** to restore original

### Test 2: Complex Website

1. Navigate to a more complex site (e.g., news website, blog)
2. Try a more specific prompt: "Apply a dark theme with better typography and spacing"
3. Test the redesign process
4. Verify content is preserved but styling is improved

### Test 3: Error Handling

1. Try with an invalid API key:
   - Enter a fake API key
   - Attempt to redesign - should show error message
2. Try with empty prompt:
   - Leave prompt field empty
   - Click redesign - should focus on prompt field
3. Try on a problematic website:
   - Navigate to a heavily dynamic site
   - Test if extension handles gracefully

## Step 5: Test Advanced Features

### Test Character Limit
1. Enter more than 500 characters in the prompt
2. Verify character counter turns red and text is truncated

### Test Options
1. Uncheck "Preserve original content" and test
2. Uncheck "Make responsive" and test
3. Test with different combinations

### Test Multiple Redesigns
1. Apply a redesign
2. Without reverting, apply another redesign
3. Verify the revert button still works correctly

## Step 6: Browser Console Testing

1. Open Chrome DevTools (F12)
2. Check the **Console** tab for any errors
3. Look for extension-related messages
4. Test redesign process while monitoring console

### Expected Console Messages
- "AI Website Redesigner content script loaded"
- "Original HTML stored for backup"
- "HTML extracted successfully"
- "Redesign applied successfully"

## Step 7: Test on Different Websites

Try the extension on various types of websites:

### Recommended Test Sites
- **Simple HTML**: `https://example.com`
- **Blog**: `https://wordpress.com`
- **E-commerce**: `https://shopify.com`
- **Documentation**: `https://developer.mozilla.org`
- **News**: `https://bbc.com`

### What to Test
- Does the extension popup load correctly?
- Can you extract HTML from the page?
- Does the AI redesign apply properly?
- Can you revert changes successfully?
- Are there any console errors?

## Troubleshooting Common Issues

### Extension Won't Load
- Check that all files are present
- Verify `manifest.json` syntax
- Look for errors in Chrome's extension management page

### API Errors
- Verify API key is correct and active
- Check internet connection
- Ensure you have API quota remaining
- Try a different, simpler prompt

### Redesign Not Applying
- Check browser console for JavaScript errors
- Try refreshing the page and testing again
- Some websites may block content modification
- Try on a simpler website first

### Revert Not Working
- Refresh the page to restore original state
- Check if original HTML was properly stored
- Look for console error messages

## Performance Testing

### Test Response Times
- Simple websites: Should complete in 10-20 seconds
- Complex websites: May take 20-40 seconds
- Very large websites: May timeout or fail

### Test Memory Usage
- Monitor Chrome's task manager during redesign
- Check for memory leaks after multiple uses
- Verify extension doesn't slow down browser

## Security Testing

### Test Data Handling
- Verify API key is stored securely
- Check that no sensitive data is logged
- Ensure only styling changes are made, not functionality

### Test on Sensitive Sites
- Try on login pages (should preserve functionality)
- Test on banking sites (use caution, test only styling)
- Verify no form data is modified

## Reporting Issues

If you find bugs or issues:

1. **Document the problem**:
   - What website were you testing on?
   - What prompt did you use?
   - What was the expected vs actual behavior?

2. **Include technical details**:
   - Chrome version
   - Extension version
   - Console error messages
   - Screenshots if helpful

3. **Steps to reproduce**:
   - Provide clear steps to recreate the issue
   - Include any specific settings or conditions

## Success Criteria

The extension passes testing if:

- ✅ Loads without errors in Chrome
- ✅ API key configuration works
- ✅ Can extract HTML from various websites
- ✅ AI redesign applies successfully
- ✅ Original content and functionality preserved
- ✅ Revert function works correctly
- ✅ Error handling works gracefully
- ✅ UI is responsive and user-friendly
- ✅ No security vulnerabilities
- ✅ Performance is acceptable

## Next Steps

After successful testing:

1. **Package for distribution** (if desired)
2. **Submit to Chrome Web Store** (optional)
3. **Gather user feedback**
4. **Plan additional features**
5. **Monitor usage and performance**

Happy testing! 🚀
