// Background script for AI Website Redesigner Chrome Extension

// Gemini API configuration
const MODEL_ID = 'gemini-2.5-pro';
const GENERATE_CONTENT_API = 'streamGenerateContent';
const GEMINI_API_BASE_URL = `https://generativelanguage.googleapis.com/v1beta/models/${MODEL_ID}:${GENERATE_CONTENT_API}`;

// Handle streaming response from Gemini API
async function handleStreamingResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';

    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.trim() === '') continue;
                if (line.startsWith('data: ')) {
                    try {
                        const jsonData = JSON.parse(line.slice(6));
                        if (jsonData.candidates && jsonData.candidates[0] && jsonData.candidates[0].content) {
                            const parts = jsonData.candidates[0].content.parts;
                            if (parts && parts[0] && parts[0].text) {
                                fullResponse += parts[0].text;
                            }
                        }
                    } catch (parseError) {
                        console.warn('Failed to parse streaming chunk:', parseError);
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error reading streaming response:', error);
        throw error;
    } finally {
        reader.releaseLock();
    }

    return fullResponse;
}

// Message listener
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    switch (message.action) {
        case 'testApiKey':
            testGeminiApiKey(message.apiKey)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response

        case 'redesignWithGemini':
            redesignWithGemini(message.apiKey, message.html, message.prompt, message.options)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response

        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

// Test Gemini API key
async function testGeminiApiKey(apiKey) {
    try {
        // Use a simpler non-streaming endpoint for testing
        const testUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent`;

        const requestBody = {
            contents: [{
                role: "user",
                parts: [{
                    text: 'Hello, this is a test message. Please respond with "API key is working".'
                }]
            }],
            generationConfig: {
                temperature: 0.1,
                maxOutputTokens: 50
            }
        };

        console.log('Testing API key with URL:', testUrl);
        console.log('Request body:', JSON.stringify(requestBody, null, 2));

        const response = await fetch(`${testUrl}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);

            let errorData;
            try {
                errorData = JSON.parse(errorText);
            } catch (parseError) {
                throw new Error(`API request failed with status ${response.status}: ${errorText}`);
            }

            throw new Error(errorData.error?.message || `API request failed with status ${response.status}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        const responseText = data.candidates?.[0]?.content?.parts?.[0]?.text || 'No response text';

        return {
            success: true,
            message: 'API key is valid',
            testResponse: responseText
        };
    } catch (error) {
        console.error('API key test failed:', error);
        return {
            success: false,
            error: error.message || 'Failed to validate API key'
        };
    }
}

// Redesign website with Gemini
async function redesignWithGemini(apiKey, html, prompt, options = {}) {
    try {
        // Clean and prepare HTML
        const cleanedHTML = cleanHTML(html);

        // Create the redesign prompt
        const systemPrompt = createRedesignPrompt(cleanedHTML, prompt, options);

        // Prepare request body with new Gemini 2.5 Pro format
        const requestBody = {
            contents: [{
                role: "user",
                parts: [{
                    text: systemPrompt
                }]
            }],
            generationConfig: {
                thinkingConfig: {
                    thinkingBudget: -1
                },
                responseMimeType: "text/plain",
                temperature: 0.7,
                maxOutputTokens: 8192,
                topP: 0.8,
                topK: 40
            },
            tools: [{
                googleSearch: {}
            }]
        };

        // Call Gemini API
        const response = await fetch(`${GEMINI_API_BASE_URL}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error?.message || 'API request failed');
        }

        // Handle streaming response
        const generatedContent = await handleStreamingResponse(response);

        if (!generatedContent) {
            throw new Error('No content generated by AI');
        }

        // Extract HTML from the response
        const redesignedHTML = extractHTMLFromResponse(generatedContent);

        if (!redesignedHTML) {
            throw new Error('Failed to extract valid HTML from AI response');
        }

        return {
            success: true,
            redesignedHTML: redesignedHTML,
            changes: 'Website redesigned with AI improvements using Gemini 2.5 Pro',
            originalPrompt: prompt
        };

    } catch (error) {
        console.error('Gemini redesign failed:', error);
        return {
            success: false,
            error: error.message || 'Failed to redesign with AI'
        };
    }
}

// Clean HTML for processing
function cleanHTML(html) {
    // Remove scripts and potentially harmful content
    let cleaned = html
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<noscript\b[^<]*(?:(?!<\/noscript>)<[^<]*)*<\/noscript>/gi, '')
        .replace(/on\w+="[^"]*"/gi, '') // Remove inline event handlers
        .replace(/javascript:/gi, ''); // Remove javascript: URLs
    
    // Limit size if too large (Gemini has token limits)
    if (cleaned.length > 50000) {
        // Try to preserve important parts
        const bodyMatch = cleaned.match(/<body[^>]*>([\s\S]*)<\/body>/i);
        if (bodyMatch) {
            const bodyContent = bodyMatch[1];
            if (bodyContent.length > 40000) {
                cleaned = `<html><head><title>Website</title></head><body>${bodyContent.substring(0, 40000)}...</body></html>`;
            }
        } else {
            cleaned = cleaned.substring(0, 50000) + '...';
        }
    }
    
    return cleaned;
}

// Create redesign prompt for Gemini
function createRedesignPrompt(html, userPrompt, options) {
    const preserveContent = options.preserveContent !== false;
    const responsiveDesign = options.responsiveDesign !== false;

    return `You are an expert web designer and developer with access to current design trends and best practices. I need you to redesign the following HTML webpage based on the user's requirements.

USER REQUIREMENTS:
${userPrompt}

DESIGN CONSTRAINTS:
- ${preserveContent ? 'PRESERVE all original text content and functionality' : 'You may modify content as needed'}
- ${responsiveDesign ? 'ENSURE the design is fully responsive and mobile-friendly' : 'Focus on desktop design'}
- Use modern CSS techniques (Flexbox, Grid, CSS Variables, modern color schemes)
- Apply current design trends and best practices (you can search for latest trends if needed)
- Improve typography, spacing, and visual hierarchy
- Ensure excellent accessibility practices (WCAG compliance)
- Keep all existing functionality intact (forms, links, scripts)
- Use inline CSS styles for immediate application
- Consider modern design systems and component libraries for inspiration

ADDITIONAL GUIDANCE:
- If the user mentions specific design styles (e.g., "modern SaaS", "minimalist", "dark theme"), research current examples of those styles
- Apply appropriate color psychology and modern color palettes
- Use proper spacing ratios (8px grid system recommended)
- Implement modern typography scales and font pairings
- Add subtle animations and transitions where appropriate
- Ensure proper contrast ratios for accessibility

ORIGINAL HTML:
${html}

Please provide ONLY the complete redesigned HTML with improved styling. The HTML should be ready to replace the current page content directly. Do not include any explanations, thinking process, or markdown formatting - just return the pure HTML code.

Start your response with <!DOCTYPE html> and ensure it's a complete, valid HTML document with all styles inline.`;
}

// Extract HTML from Gemini response
function extractHTMLFromResponse(response) {
    // Remove any markdown code blocks
    let html = response.replace(/```html\s*/gi, '').replace(/```\s*$/gi, '');
    
    // Ensure it starts with DOCTYPE or html tag
    if (!html.trim().toLowerCase().startsWith('<!doctype') && 
        !html.trim().toLowerCase().startsWith('<html')) {
        // Try to find HTML content in the response
        const htmlMatch = html.match(/<!DOCTYPE html>[\s\S]*<\/html>/i) || 
                         html.match(/<html[\s\S]*<\/html>/i);
        if (htmlMatch) {
            html = htmlMatch[0];
        } else {
            return null;
        }
    }
    
    // Basic validation
    if (html.includes('<html') && html.includes('</html>')) {
        return html.trim();
    }
    
    return null;
}

// Extension installation handler
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        console.log('AI Website Redesigner extension installed');
        
        // Set default settings
        chrome.storage.sync.set({
            extensionVersion: '1.0.0',
            installDate: new Date().toISOString()
        });
    }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
    // This will open the popup, but we can add additional logic here if needed
    console.log('Extension icon clicked on tab:', tab.url);
});
