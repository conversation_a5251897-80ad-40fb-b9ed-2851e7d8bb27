// Background script for AI Website Redesigner Chrome Extension

// Gemini API configuration
const GEMINI_API_BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

// Message listener
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.action) {
        case 'testApiKey':
            testGeminiApiKey(message.apiKey)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response

        case 'redesignWithGemini':
            redesignWithGemini(message.apiKey, message.html, message.prompt, message.options)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response

        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

// Test Gemini API key
async function testGeminiApiKey(apiKey) {
    try {
        const response = await fetch(`${GEMINI_API_BASE_URL}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: 'Hello, this is a test message. Please respond with "API key is working".'
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    maxOutputTokens: 50
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error?.message || 'Invalid API key');
        }

        const data = await response.json();
        const responseText = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
        
        return {
            success: true,
            message: 'API key is valid',
            testResponse: responseText
        };
    } catch (error) {
        console.error('API key test failed:', error);
        return {
            success: false,
            error: error.message || 'Failed to validate API key'
        };
    }
}

// Redesign website with Gemini
async function redesignWithGemini(apiKey, html, prompt, options = {}) {
    try {
        // Clean and prepare HTML
        const cleanedHTML = cleanHTML(html);
        
        // Create the redesign prompt
        const systemPrompt = createRedesignPrompt(cleanedHTML, prompt, options);
        
        // Call Gemini API
        const response = await fetch(`${GEMINI_API_BASE_URL}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: systemPrompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.7,
                    maxOutputTokens: 8192,
                    topP: 0.8,
                    topK: 40
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error?.message || 'API request failed');
        }

        const data = await response.json();
        const generatedContent = data.candidates?.[0]?.content?.parts?.[0]?.text;
        
        if (!generatedContent) {
            throw new Error('No content generated by AI');
        }

        // Extract HTML from the response
        const redesignedHTML = extractHTMLFromResponse(generatedContent);
        
        if (!redesignedHTML) {
            throw new Error('Failed to extract valid HTML from AI response');
        }

        return {
            success: true,
            redesignedHTML: redesignedHTML,
            changes: 'Website redesigned with AI improvements',
            originalPrompt: prompt
        };

    } catch (error) {
        console.error('Gemini redesign failed:', error);
        return {
            success: false,
            error: error.message || 'Failed to redesign with AI'
        };
    }
}

// Clean HTML for processing
function cleanHTML(html) {
    // Remove scripts and potentially harmful content
    let cleaned = html
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<noscript\b[^<]*(?:(?!<\/noscript>)<[^<]*)*<\/noscript>/gi, '')
        .replace(/on\w+="[^"]*"/gi, '') // Remove inline event handlers
        .replace(/javascript:/gi, ''); // Remove javascript: URLs
    
    // Limit size if too large (Gemini has token limits)
    if (cleaned.length > 50000) {
        // Try to preserve important parts
        const bodyMatch = cleaned.match(/<body[^>]*>([\s\S]*)<\/body>/i);
        if (bodyMatch) {
            const bodyContent = bodyMatch[1];
            if (bodyContent.length > 40000) {
                cleaned = `<html><head><title>Website</title></head><body>${bodyContent.substring(0, 40000)}...</body></html>`;
            }
        } else {
            cleaned = cleaned.substring(0, 50000) + '...';
        }
    }
    
    return cleaned;
}

// Create redesign prompt for Gemini
function createRedesignPrompt(html, userPrompt, options) {
    const preserveContent = options.preserveContent !== false;
    const responsiveDesign = options.responsiveDesign !== false;

    return `You are an expert web designer and developer. I need you to redesign the following HTML webpage based on the user's requirements.

USER REQUIREMENTS:
${userPrompt}

DESIGN CONSTRAINTS:
- ${preserveContent ? 'PRESERVE all original text content and functionality' : 'You may modify content as needed'}
- ${responsiveDesign ? 'ENSURE the design is fully responsive and mobile-friendly' : 'Focus on desktop design'}
- Use modern CSS techniques (Flexbox, Grid, modern color schemes)
- Improve typography, spacing, and visual hierarchy
- Ensure good accessibility practices
- Keep all existing functionality intact
- Use inline CSS styles for immediate application

ORIGINAL HTML:
${html}

Please provide ONLY the complete redesigned HTML with improved styling. The HTML should be ready to replace the current page content directly. Do not include any explanations or markdown formatting - just return the pure HTML code.

Start your response with <!DOCTYPE html> and ensure it's a complete, valid HTML document.`;
}

// Extract HTML from Gemini response
function extractHTMLFromResponse(response) {
    // Remove any markdown code blocks
    let html = response.replace(/```html\s*/gi, '').replace(/```\s*$/gi, '');
    
    // Ensure it starts with DOCTYPE or html tag
    if (!html.trim().toLowerCase().startsWith('<!doctype') && 
        !html.trim().toLowerCase().startsWith('<html')) {
        // Try to find HTML content in the response
        const htmlMatch = html.match(/<!DOCTYPE html>[\s\S]*<\/html>/i) || 
                         html.match(/<html[\s\S]*<\/html>/i);
        if (htmlMatch) {
            html = htmlMatch[0];
        } else {
            return null;
        }
    }
    
    // Basic validation
    if (html.includes('<html') && html.includes('</html>')) {
        return html.trim();
    }
    
    return null;
}

// Extension installation handler
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        console.log('AI Website Redesigner extension installed');
        
        // Set default settings
        chrome.storage.sync.set({
            extensionVersion: '1.0.0',
            installDate: new Date().toISOString()
        });
    }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
    // This will open the popup, but we can add additional logic here if needed
    console.log('Extension icon clicked on tab:', tab.url);
});
