// Test script for Gemini 2.5 Pro API integration
// This can be run in the browser console to test the API

const MODEL_ID = 'gemini-2.5-pro';
const GENERATE_CONTENT_API = 'streamGenerateContent';
const GEMINI_API_BASE_URL = `https://generativelanguage.googleapis.com/v1beta/models/${MODEL_ID}:${GENERATE_CONTENT_API}`;

// Test function - replace YOUR_API_KEY with actual key
async function testGemini25Pro(apiKey, testPrompt = "Hello! Please respond with a simple greeting.") {
    try {
        console.log('Testing Gemini 2.5 Pro API...');
        
        const requestBody = {
            contents: [{
                role: "user",
                parts: [{
                    text: testPrompt
                }]
            }],
            generationConfig: {
                thinkingConfig: {
                    thinkingBudget: -1
                },
                responseMimeType: "text/plain",
                temperature: 0.7,
                maxOutputTokens: 1000
            },
            tools: [{
                googleSearch: {}
            }]
        };

        console.log('Request body:', JSON.stringify(requestBody, null, 2));

        const response = await fetch(`${GEMINI_API_BASE_URL}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);

        if (!response.ok) {
            const errorData = await response.json();
            console.error('API Error:', errorData);
            throw new Error(errorData.error?.message || 'API request failed');
        }

        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullResponse = '';
        
        console.log('Reading streaming response...');
        
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value, { stream: true });
            console.log('Chunk received:', chunk);
            
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.trim() === '') continue;
                if (line.startsWith('data: ')) {
                    try {
                        const jsonData = JSON.parse(line.slice(6));
                        console.log('Parsed chunk:', jsonData);
                        
                        if (jsonData.candidates && jsonData.candidates[0] && jsonData.candidates[0].content) {
                            const parts = jsonData.candidates[0].content.parts;
                            if (parts && parts[0] && parts[0].text) {
                                fullResponse += parts[0].text;
                            }
                        }
                    } catch (parseError) {
                        console.warn('Failed to parse streaming chunk:', parseError);
                    }
                }
            }
        }

        console.log('Full response:', fullResponse);
        return {
            success: true,
            response: fullResponse
        };

    } catch (error) {
        console.error('Test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Example usage:
// testGemini25Pro('YOUR_API_KEY_HERE').then(result => console.log('Test result:', result));

// Test with design prompt
async function testDesignPrompt(apiKey) {
    const designPrompt = `You are a web design expert. Please suggest 3 modern color schemes for a SaaS landing page in 2024. Research current trends if needed and provide hex codes.`;
    
    return await testGemini25Pro(apiKey, designPrompt);
}

// Export for use in extension
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testGemini25Pro, testDesignPrompt };
}

console.log('Gemini 2.5 Pro test functions loaded. Use testGemini25Pro(apiKey) to test.');
